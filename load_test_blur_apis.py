#!/usr/bin/env python3
"""
Load test two "blur detection" APIs across a set of image URLs.
Sends N requests per API per image and measures latency and (optionally) a numeric score
from the API response. Outputs CSVs and charts for analysis.

Usage:
    python load_test_blur_apis.py --runs 100 --concurrency 20

Notes:
- This script is resilient to unknown JSON shapes. It heuristically looks for a numeric "score"
  using typical blur keys (e.g., "blur", "blurriness", "score", "variance", "sharpness").
- If a score can't be found in a response, it's recorded as blank and only latency is analyzed.
- Charts are saved as PNG files in the ./results/ folder.

Requirements:
    pip install aiohttp pandas matplotlib
"""

import argparse
import asyncio
import json
import os
import sys
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import aiohttp
import pandas as pd
import matplotlib.pyplot as plt


# -------------------------------
# Configuration
# -------------------------------

IMAGE_URLS = [
    "https://imagedetectionv2.blob.core.windows.net/test/500%20kb.jpg",
    "https://imagedetectionv2.blob.core.windows.net/test/1%20mb.png",
    "https://imagedetectionv2.blob.core.windows.net/test/IMG_1734.jpg",
    "https://imagedetectionv2.blob.core.windows.net/test/IMG_1735.jpg",
    "https://imagedetectionv2.blob.core.windows.net/test/IMG_1765.jpg",
    "https://imagedetectionv2.blob.core.windows.net/test/IMG_1764.jpg",
    "https://imagedetectionv2.blob.core.windows.net/test/IMG_1962.jpg",
    "https://imagedetectionv2.blob.core.windows.net/test/2%20mb.jpeg",
    "https://imagedetectionv2.blob.core.windows.net/test/IMG_1766.HEIC",
    "https://imagedetectionv2.blob.core.windows.net/test/IMG_1734.HEIC",
    "https://imagedetectionv2.blob.core.windows.net/test/IMG_1735.HEIC",
    "https://imagedetectionv2.blob.core.windows.net/test/IMG_1737.HEIC",
    "https://imagedetectionv2.blob.core.windows.net/test/IMG_1962.HEIC",
    "https://imagedetectionv2.blob.core.windows.net/test/9%20mb.jpeg",
    "https://imagedetectionv2.blob.core.windows.net/test/30%20mb.jpeg",
]

# Define the two APIs we are benchmarking. Each entry provides:
# - name: label used in results
# - url: endpoint
# - method: HTTP method (currently only POST expected)
# - build_payload(image_url) -> dict: function to build request JSON body
# - headers: HTTP headers
API_DEFINITIONS = [
    {
        "name": "DescribeAPI",
        "url": "http://fai-imagedescribe.fieldassist.io:8080/analyze",
        "method": "POST",
        "headers": {"Content-Type": "application/json"},
        "build_payload": lambda image_url: {"url": image_url, "is_ocr": False},
    },
    {
        "name": "BlurAPI",
        "url": "https://blur.lemoncoast-53375c73.centralindia.azurecontainerapps.io/detect-blur",
        "method": "POST",
        "headers": {"Content-Type": "application/json"},
        "build_payload": lambda image_url: {"image_url": image_url},
    },
]


# -------------------------------
# Heuristic score extraction
# -------------------------------

SCORE_KEYS = [
    # common
    "score", "value", "metric", "confidence",
    # blur-ish keys
    "blur", "blurriness", "variance", "laplacian", "sharpness", "focus",
    # nested/alternative
    "quality", "clarity",
]


def try_extract_numeric_score(obj: Any) -> Optional[float]:
    """
    Try to find a numeric score in a JSON-like object using heuristic keys.
    Returns float if found, else None.
    """
    try:
        # If the entire object is a number
        if isinstance(obj, (int, float)):
            return float(obj)

        # If it's a list, try each element
        if isinstance(obj, list):
            for item in obj:
                v = try_extract_numeric_score(item)
                if v is not None:
                    return v
            return None

        # If it's a dict, check keys first, then recurse
        if isinstance(obj, dict):
            # direct key matches
            for k in SCORE_KEYS:
                if k in obj and isinstance(obj[k], (int, float)):
                    return float(obj[k])

            # deep search
            for v in obj.values():
                candidate = try_extract_numeric_score(v)
                if candidate is not None:
                    return candidate

    except Exception:
        return None

    return None


# -------------------------------
# Load test logic
# -------------------------------

async def fire_one(session: aiohttp.ClientSession, api: Dict[str, Any], image_url: str) -> Dict[str, Any]:
    """
    Send one request and capture metrics.
    """
    start = time.perf_counter()
    error = None
    status = None
    score: Optional[float] = None
    body_text: Optional[str] = None

    try:
        payload = api["build_payload"](image_url)

        async with session.post(api["url"], json=payload, headers=api["headers"]) as resp:
            status = resp.status
            body_text = await resp.text()

            # Try JSON parse
            try:
                data = json.loads(body_text)
            except Exception:
                data = None

            if data is not None:
                score = try_extract_numeric_score(data)

    except Exception as ex:
        error = str(ex)

    elapsed_ms = (time.perf_counter() - start) * 1000.0

    return {
        "ts": datetime.now().isoformat(timespec="seconds"),
        "image_url": image_url,
        "api": api["name"],
        "status": status,
        "latency_ms": elapsed_ms,
        "score": score,
        "error": error,
        "raw": body_text,
    }


async def run_load(runs: int, concurrency: int, timeout_s: int) -> List[Dict[str, Any]]:
    """
    Execute the load test: for each image and each API, send `runs` requests.
    Concurrency is globally limited.
    """
    connector = aiohttp.TCPConnector(limit=0, ssl=False)
    timeout = aiohttp.ClientTimeout(total=timeout_s)

    sem = asyncio.Semaphore(concurrency)
    results: List[Dict[str, Any]] = []

    async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:

        async def bound_call(api, image_url):
            async with sem:
                return await fire_one(session, api, image_url)

        tasks = []
        for image_url in IMAGE_URLS:
            for api in API_DEFINITIONS:
                for _ in range(runs):
                    tasks.append(asyncio.create_task(bound_call(api, image_url)))

        # Gather with progress
        completed = 0
        total = len(tasks)
        for coro in asyncio.as_completed(tasks):
            res = await coro
            results.append(res)
            completed += 1
            if completed % 50 == 0 or completed == total:
                print(f"Completed {completed}/{total} requests", flush=True)

    return results


# -------------------------------
# Analysis & Visualization
# -------------------------------

def analyze_and_visualize(df: pd.DataFrame, out_dir: str) -> None:
    """
    Produce summary CSVs and charts:
      - latency_summary.csv: per image & API mean/p50/p95 latency, success rate
      - score_summary.csv:   per image & API mean/std/valid_count for score
      - api_diff_scores.csv: per image difference of mean scores between APIs (if both available)
      - Charts: PNGs for avg latency and score stddev heatmaps/bars
    """
    os.makedirs(out_dir, exist_ok=True)

    # Normalize fields
    df["ok"] = df["status"].apply(lambda s: 1 if (isinstance(s, int) and 200 <= s < 300) else 0)

    # Per-image & API latency summary
    def p95(x):
        return x.quantile(0.95)

    lat_summary = (
        df.groupby(["image_url", "api"])
          .agg(
              requests=("latency_ms", "count"),
              success_rate=("ok", "mean"),
              avg_latency_ms=("latency_ms", "mean"),
              p50_latency_ms=("latency_ms", "median"),
              p95_latency_ms=("latency_ms", p95),
          )
          .reset_index()
    )
    lat_summary["success_rate"] = (lat_summary["success_rate"] * 100).round(2)

    # Score summary (if present)
    score_df = df.dropna(subset=["score"]).copy()
    if not score_df.empty:
        score_summary = (
            score_df.groupby(["image_url", "api"])
                    .agg(
                        valid_scores=("score", "count"),
                        mean_score=("score", "mean"),
                        std_score=("score", "std"),
                    )
                    .reset_index()
        )
    else:
        score_summary = pd.DataFrame(columns=["image_url", "api", "valid_scores", "mean_score", "std_score"])

    # Pairwise difference of mean scores between APIs per image (only if both present)
    api_names = sorted(df["api"].unique().tolist())
    diff_rows = []
    if not score_df.empty and len(api_names) >= 2:
        pivot = score_summary.pivot(index="image_url", columns="api", values="mean_score")
        for i in range(len(api_names)):
            for j in range(i + 1, len(api_names)):
                a, b = api_names[i], api_names[j]
                if a in pivot.columns and b in pivot.columns:
                    tmp = pivot[[a, b]].dropna()
                    for img, row in tmp.iterrows():
                        diff_rows.append({
                            "image_url": img,
                            "api_a": a,
                            "api_b": b,
                            "mean_score_diff": row[a] - row[b]
                        })
    score_diffs = pd.DataFrame(diff_rows) if diff_rows else pd.DataFrame(
        columns=["image_url", "api_a", "api_b", "mean_score_diff"]
    )

    # Save CSVs
    lat_csv = os.path.join(out_dir, "latency_summary.csv")
    score_csv = os.path.join(out_dir, "score_summary.csv")
    diff_csv = os.path.join(out_dir, "api_diff_scores.csv")

    lat_summary.to_csv(lat_csv, index=False)
    score_summary.to_csv(score_csv, index=False)
    score_diffs.to_csv(diff_csv, index=False)

    # Save raw results too
    df.to_csv(os.path.join(out_dir, "raw_results.csv"), index=False)

    # ------------- Charts -------------
    # Chart 1: Average latency per API per image (bar)
    plt.figure(figsize=(12, 7))
    # Build a grouped bar chart
    # We'll pivot to have APIs as columns
    pivot_lat = lat_summary.pivot(index="image_url", columns="api", values="avg_latency_ms").fillna(0)
    pivot_lat.plot(kind="bar")
    plt.title("Average Latency (ms) per API per Image")
    plt.xlabel("Image URL")
    plt.ylabel("Average Latency (ms)")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    plt.savefig(os.path.join(out_dir, "avg_latency_per_api_per_image.png"))
    plt.close()

    # Chart 2: p95 latency per API per image
    plt.figure(figsize=(12, 7))
    pivot_p95 = lat_summary.pivot(index="image_url", columns="api", values="p95_latency_ms").fillna(0)
    pivot_p95.plot(kind="bar")
    plt.title("p95 Latency (ms) per API per Image")
    plt.xlabel("Image URL")
    plt.ylabel("p95 Latency (ms)")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    plt.savefig(os.path.join(out_dir, "p95_latency_per_api_per_image.png"))
    plt.close()

    # Chart 3: Score std dev per API per image (if scores exist)
    if not score_df.empty:
        plt.figure(figsize=(12, 7))
        pivot_std = score_summary.pivot(index="image_url", columns="api", values="std_score").fillna(0)
        pivot_std.plot(kind="bar")
        plt.title("Score Standard Deviation per API per Image")
        plt.xlabel("Image URL")
        plt.ylabel("Std Dev of Score")
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()
        plt.savefig(os.path.join(out_dir, "score_stddev_per_api_per_image.png"))
        plt.close()

    # Chart 4: Success rate per API per image
    plt.figure(figsize=(12, 7))
    pivot_sr = lat_summary.pivot(index="image_url", columns="api", values="success_rate").fillna(0)
    pivot_sr.plot(kind="bar")
    plt.title("Success Rate (%) per API per Image")
    plt.xlabel("Image URL")
    plt.ylabel("Success Rate (%)")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    plt.savefig(os.path.join(out_dir, "success_rate_per_api_per_image.png"))
    plt.close()

    print(f"Saved analysis to: {out_dir}")
    print(f"- {lat_csv}")
    print(f"- {score_csv}")
    print(f"- {diff_csv}")


def ensure_out_dir(base: str = "results") -> str:
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    out_dir = os.path.join(base, f"run_{ts}")
    os.makedirs(out_dir, exist_ok=True)
    return out_dir


def parse_args() -> argparse.Namespace:
    p = argparse.ArgumentParser(description="Load test two blur APIs across images.")
    p.add_argument("--runs", type=int, default=100, help="Requests per API per image (default: 100)")
    p.add_argument("--concurrency", type=int, default=20, help="Global max concurrent requests (default: 20)")
    p.add_argument("--timeout", type=int, default=120, help="Total request timeout in seconds (default: 120)")
    p.add_argument("--out", type=str, default="results", help="Output folder (default: results)")
    return p.parse_args()


def main() -> int:
    args = parse_args()
    print(f"Images: {len(IMAGE_URLS)} | APIs: {len(API_DEFINITIONS)} | Runs per API/Image: {args.runs}")
    out_dir = ensure_out_dir(args.out)

    try:
        results = asyncio.run(run_load(args.runs, args.concurrency, args.timeout))
    except KeyboardInterrupt:
        print("\nInterrupted by user.")
        return 1

    df = pd.DataFrame(results, columns=["ts", "image_url", "api", "status", "latency_ms", "score", "error", "raw"])

    # Write raw as NDJSON for debugging (optional)
    with open(os.path.join(out_dir, "raw_results.ndjson"), "w", encoding="utf-8") as f:
        for _, row in df.iterrows():
            f.write(json.dumps(row.to_dict(), ensure_ascii=False) + "\n")

    analyze_and_visualize(df, out_dir)

    print("Done.")
    return 0


if __name__ == "__main__":
    sys.exit(main())
